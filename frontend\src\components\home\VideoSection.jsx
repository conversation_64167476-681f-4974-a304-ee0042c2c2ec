import { useState, useRef } from 'react';
import animation11 from '../../assets/photos/animation11.png';

const VideoSection = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [playingVideo, setPlayingVideo] = useState(null);
  const coverflowRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);

  // Video gallery data
  const videos = [
    {
      id: 1,
      youtubeId: 'rvmEz9RiDZ8',
      title: 'Discover North East India',
      description: 'Experience the magical landscapes and rich culture',
      thumbnail: animation11,
      duration: '3:45'
    },
    {
      id: 2,
      youtubeId: 'dQw4w9WgXcQ', // Replace with actual video IDs
      title: 'Sikkim Adventures',
      description: 'Journey through the pristine mountains of Sikkim',
      thumbnail: animation11,
      duration: '5:20'
    },
    {
      id: 3,
      youtubeId: 'jNQXAC9IVRw', // Replace with actual video IDs
      title: 'Assam Tea Gardens',
      description: 'Explore the lush tea gardens and wildlife',
      thumbnail: animation11,
      duration: '4:15'
    },
    {
      id: 4,
      youtubeId: 'M7lc1UVf-VE', // Replace with actual video IDs
      title: 'Meghalaya Waterfalls',
      description: 'Witness the breathtaking waterfalls and caves',
      thumbnail: animation11,
      duration: '6:30'
    }
  ];

  // Handle mouse events for dragging
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartX(e.clientX);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;

    const deltaX = e.clientX - startX;
    if (Math.abs(deltaX) > 50) {
      if (deltaX > 0 && activeIndex > 0) {
        setActiveIndex(activeIndex - 1);
      } else if (deltaX < 0 && activeIndex < videos.length - 1) {
        setActiveIndex(activeIndex + 1);
      }
      setIsDragging(false);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Touch events for mobile
  const handleTouchStart = (e) => {
    setStartX(e.touches[0].clientX);
  };

  const handleTouchMove = (e) => {
    const deltaX = e.touches[0].clientX - startX;
    if (Math.abs(deltaX) > 50) {
      if (deltaX > 0 && activeIndex > 0) {
        setActiveIndex(activeIndex - 1);
      } else if (deltaX < 0 && activeIndex < videos.length - 1) {
        setActiveIndex(activeIndex + 1);
      }
    }
  };

  const handleTouchEnd = () => {
    // Touch end logic if needed
  };

  // Calculate card positioning for coverflow effect
  const getCardStyle = (index) => {
    const distance = index - activeIndex;
    const absDistance = Math.abs(distance);

    // Only show cards within reasonable range
    if (absDistance > 2) return { display: 'none' };

    const translateX = distance * 85; // Horizontal offset
    const translateZ = -absDistance * 60; // Depth
    const rotateY = distance * 12; // Rotation
    const scale = 1 - absDistance * 0.12; // Scale
    const opacity = 1 - absDistance * 0.3; // Opacity

    return {
      transform: `translateX(${translateX}%) translateZ(${translateZ}px) rotateY(${rotateY}deg) scale(${scale})`,
      opacity: Math.max(opacity, 0.4),
      zIndex: 10 - absDistance,
    };
  };

  const handlePlayVideo = (videoId) => {
    setPlayingVideo(videoId);
  };

  const handleCloseVideo = () => {
    setPlayingVideo(null);
  };

  // Navigation functions
  const goToPrevious = () => {
    setActiveIndex(prev => prev > 0 ? prev - 1 : videos.length - 1);
  };

  const goToNext = () => {
    setActiveIndex(prev => prev < videos.length - 1 ? prev + 1 : 0);
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light mb-4 font-serif">
            Discover the{" "}
            <span className='text-blue-400'>Magic</span> of North East India
          </h2>
          <div className="w-[1px] h-16 bg-gray-200 mx-auto mb-4"></div>
          <p className="max-w-xl mx-auto text-gray-600">
            Experience the beauty of North East India through our curated video collection
          </p>
        </div>

        {/* Coverflow Video Gallery */}
        <div
          className="relative h-[600px] overflow-hidden [perspective:1200px]"
          ref={coverflowRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div className="absolute inset-0 flex items-center justify-center">
            {videos.map((video, index) => (
              <div
                key={video.id}
                className="absolute w-[380px] h-[480px] rounded-2xl transition-all duration-700 ease-out bg-white overflow-hidden cursor-pointer shadow-xl"
                style={{
                  ...getCardStyle(index),
                  boxShadow: index === activeIndex
                    ? "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)"
                    : "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
                }}
                onClick={() => setActiveIndex(index)}
              >
                {/* Video Thumbnail */}
                <div className="relative h-[280px] overflow-hidden">
                  <img
                    src={video.thumbnail}
                    alt={video.title}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                  />

                  {/* Duration Badge */}
                  <div className="absolute bottom-3 right-3 bg-black/80 text-white px-2 py-1 rounded text-sm font-medium">
                    {video.duration}
                  </div>

                  {/* Play Button Overlay */}
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePlayVideo(video.id);
                      }}
                      className="w-16 h-16 bg-white/90 hover:bg-white rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path d="M8 5v14l11-7-11-7z" fill="#1f2937" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Video Info */}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
                    {video.title}
                  </h3>
                  <p className="text-gray-600 text-sm line-clamp-3 mb-4">
                    {video.description}
                  </p>

                  {/* Action Button */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePlayVideo(video.id);
                    }}
                    className="w-full py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-xl transition-colors duration-300 flex items-center justify-center"
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="mr-2"
                    >
                      <path d="M8 5v14l11-7-11-7z" fill="currentColor" />
                    </svg>
                    Watch Video
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Controls */}
        <div className="flex justify-center items-center mt-8 space-x-4">
          <button
            onClick={goToPrevious}
            className="w-12 h-12 bg-white hover:bg-gray-50 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-105"
            disabled={activeIndex === 0}
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="rotate-180"
            >
              <path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>

          {/* Dots Indicator */}
          <div className="flex space-x-2">
            {videos.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === activeIndex
                    ? 'bg-indigo-600 scale-125'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>

          <button
            onClick={goToNext}
            className="w-12 h-12 bg-white hover:bg-gray-50 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-105"
            disabled={activeIndex === videos.length - 1}
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>
        </div>
      </div>

      {/* Video Modal with Industry Standard YouTube Controls */}
      {playingVideo && (
        <div className="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4">
          <div className="relative w-full max-w-6xl mx-auto">
            {/* Close Button */}
            <button
              onClick={handleCloseVideo}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors z-10"
            >
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>

            {/* Video Container */}
            <div className="relative w-full bg-black rounded-lg overflow-hidden shadow-2xl" style={{ paddingTop: "56.25%" }}>
              <iframe
                className="absolute inset-0 w-full h-full"
                src={`https://www.youtube.com/embed/${videos.find(v => v.id === playingVideo)?.youtubeId}?autoplay=1&rel=0&modestbranding=0&playsinline=1&controls=1&cc_load_policy=1&iv_load_policy=3&enablejsapi=1&origin=${window.location.origin}`}
                title="YouTube video player"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
                style={{ border: 'none' }}
              ></iframe>
            </div>

            {/* Video Info Below */}
            <div className="mt-4 text-center">
              <h3 className="text-2xl font-semibold text-white mb-2">
                {videos.find(v => v.id === playingVideo)?.title}
              </h3>
              <p className="text-gray-300">
                {videos.find(v => v.id === playingVideo)?.description}
              </p>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default VideoSection;
